<?php

namespace App\Http\Controllers;

use App\Models\EventAttendee;
use App\Models\EventLike;
use App\Models\User;
use App\Models\EventSocialLink;
use App\Models\EventSponsor;
use App\Services\GeocodingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\EventNotification;
use App\Models\EventShare;
use App\Models\MeetingLink;
use App\Models\EventRetailer;
use App\Models\Interest;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class EventController extends Controller
{
    public function byCategory(Request $request, $id): JsonResponse
    {
        $perPage = $request->input('per_page', 15);
        $events = Event::where('category_id', $id)->paginate($perPage);

        return response()->json([
            'events' => $events
        ], 200);
    }

    /**
     * Get paginated tickets for a specific event
     *
     * @param Request $request
     * @param int $id Event ID
     * @return JsonResponse
     */
    public function eventTickets(Request $request, $id): JsonResponse
    {
        $event = Event::find($id);

        if (!$event) {
            return response()->json(['error' => 'Event not found'], 404);
        }

        $perPage = $request->query('per_page', 15);
        $page = $request->query('page', 1);

        $sortBy = $request->query('sort_by', 'created_at');
        $sortDir = $request->query('sort_dir', 'desc');

        $tickets = $event->tickets()
            ->with([
                'purchases' => function ($query) {
                    $query->with('user:id,name,email')
                          ->where('status', 'completed')
                          ->latest();
                },
                'tier:id,name,price'
            ])
            ->when($request->has('search'), function ($query) use ($request) {
                $search = $request->query('search');
                return $query->where(function ($q) use ($search) {
                    $q->where('uuid', 'LIKE', "%{$search}%")
                      ->orWhere('name', 'LIKE', "%{$search}%")
                      ->orWhereHas('purchases', function ($purchaseQuery) use ($search) {
                          $purchaseQuery->where('purchase_reference', 'LIKE', "%{$search}%")
                                       ->orWhere('attendee_name', 'LIKE', "%{$search}%")
                                       ->orWhere('attendee_email', 'LIKE', "%{$search}%")
                                       ->orWhereHas('user', function ($userQuery) use ($search) {
                                           $userQuery->where('name', 'LIKE', "%{$search}%")
                                                    ->orWhere('email', 'LIKE', "%{$search}%");
                                       });
                      });
                });
            })
            ->when($request->has('status') || $request->has('scan_status'), function ($query) use ($request) {
                $status = $request->query('status') ?? $request->query('scan_status');
                if ($status === 'scanned') {
                    return $query->where(function ($q) {
                        $q->where('scanned', true)
                          ->orWhereHas('purchases', function ($purchaseQuery) {
                              $purchaseQuery->where('scanned', true);
                          });
                    });
                } elseif ($status === 'unscanned') {
                    return $query->where(function ($q) {
                        $q->where(function ($innerQ) {
                            $innerQ->where('scanned', false)
                                   ->orWhereNull('scanned');
                        })
                        ->whereDoesntHave('purchases', function ($purchaseQuery) {
                            $purchaseQuery->where('scanned', true);
                        });
                    });
                }
                return $query;
            })
            ->when($request->has('purchase_status'), function ($query) use ($request) {
                $purchaseStatus = $request->query('purchase_status');
                if ($purchaseStatus === 'bought') {
                    return $query->whereHas('purchases', function ($purchaseQuery) {
                        $purchaseQuery->where('status', 'completed');
                    });
                } elseif ($purchaseStatus === 'not_bought') {
                    return $query->whereDoesntHave('purchases', function ($purchaseQuery) {
                        $purchaseQuery->where('status', 'completed');
                    });
                }
                return $query;
            })
            ->when($request->has('date_from') && $request->has('date_to'), function ($query) use ($request) {
                $dateFrom = $request->query('date_from');
                $dateTo = $request->query('date_to');
                return $query->whereBetween('created_at', [$dateFrom, $dateTo]);
            })
            ->orderBy($sortBy, $sortDir)
            ->paginate($perPage);

        $tickets->getCollection()->transform(function ($ticket) {
            $latestPurchase = $ticket->purchases->first();

            return [
                'id' => $ticket->id,
                'uuid' => $ticket->uuid,
                'name' => $ticket->name,
                'created_at' => $ticket->created_at,
                'scanned' => $ticket->scanned || ($latestPurchase && $latestPurchase->scanned),
                'scanned_at' => $ticket->scanned_at ?? ($latestPurchase ? $latestPurchase->scanned_at : null),
                'status' => ($ticket->scanned || ($latestPurchase && $latestPurchase->scanned)) ? 'scanned' : 'unscanned',
                'is_bought' => $ticket->purchases->count() > 0,
                'purchase_count' => $ticket->purchases->count(),
                'tier' => $ticket->tier,
                'purchase_reference' => $latestPurchase ? $latestPurchase->purchase_reference : null,
                'customer' => $latestPurchase ? (
                    $latestPurchase->attendee_name ??
                    ($latestPurchase->user ? $latestPurchase->user->name : 'Not assigned')
                ) : 'Not assigned',
                'email' => $latestPurchase ? (
                    $latestPurchase->attendee_email ??
                    ($latestPurchase->user ? $latestPurchase->user->email : 'Not assigned')
                ) : 'Not assigned',
                'purchaseDate' => $latestPurchase ? $latestPurchase->created_at->format('M d, Y H:i') : null,
                'event_title' => $ticket->event->title ?? '',
                'ticket' => $ticket,
                'user' => $latestPurchase ? $latestPurchase->user : null,
            ];
        });

        return response()->json($tickets, 200);
    }


    public function index(Request $request)
    {
        $events = Event::whereNotNull('published_at')
            ->with(['attendees.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with(['likes.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with('tiers')
            ->with('meeting_link')
            ->withCount('attendees')
            ->withCount('likes')
            ->withCount('shares')
            ->orderBy('published_at', 'DESC')
            ->paginate($request->per_page ?? 10);

        foreach ($events as $event) {
            $attendeesAvatars = $event->attendees->pluck('user.avatar')->filter()->toArray();
            $likesAvatars = $event->likes->pluck('user.avatar')->filter()->toArray();

            $event->attendees_avatars = json_encode($attendeesAvatars);
            $event->likes_avatars = json_encode($likesAvatars);
            $event->is_liked = $event->likes->count() > 0;
            unset($event->likes);
            unset($event->attendees);
        }

        return response()->json(['events' => $events], 200);
    }

    public function getTrending(Request $request)
    {
        $events = Event::orderBy('views', 'desc')
            ->whereNotNull('published_at')
            ->with(['attendees.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with(['likes.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with('tiers')
            ->with('meeting_link')
            ->withCount('attendees')
            ->withCount('likes')
            ->withCount('shares')
            ->get();

        foreach ($events as $event) {
            $event->engagement_score = $this->calculateEngagementScore($event);

            $attendeesAvatars = $event->attendees->pluck('user.avatar')->filter()->toArray();
            $likesAvatars = $event->likes->pluck('user.avatar')->filter()->toArray();

            $event->attendees_avatars = json_encode($attendeesAvatars);
            $event->likes_avatars = json_encode($likesAvatars);
            $event->is_liked = $event->likes_count > 0;

            unset($event->likes);
            unset($event->attendees);
        }

        $events = $events->sortByDesc('engagement_score');

        $perPage = $request->per_page ?? 10;
        $paginatedEvents = new \Illuminate\Pagination\LengthAwarePaginator(
            $events->forPage($request->page ?? 1, $perPage),
            $events->count(),
            $perPage,
            $request->page ?? 1,
            ['path' => url()->current()]
        );

        return response()->json(['events' => $paginatedEvents], 200);
    }


    private function calculateEngagementScore(Event $event)
    {
        $viewWeight = 0.5;
        $likeWeight = 0.2;
        $attendeeWeight = 0.2;
        $shareWeight = 0.1;

        $score = ($event->views * $viewWeight) +
            ($event->likes_count * $likeWeight) +
            ($event->attendees_count * $attendeeWeight) +
            ($event->shares_count * $shareWeight);

        return $score;
    }

    public function recommendEvents(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'message' => 'Please login to see recommended events based on your interests',
                'events' => (object)[
                    'data' => [],
                    'total' => 0,
                    'per_page' => $request->per_page ?? 10,
                    'current_page' => 1
                ]
            ], 401);
        }

        $userId = $user->id;
        $userInterests = Interest::where('user_id', $userId)->pluck('category_id')->toArray();

        if (empty($userInterests)) {
            return response()->json([
                'message' => 'No interests found. Please update your profile to get personalized recommendations.',
                'events' => (object)[
                    'data' => [],
                    'total' => 0,
                    'per_page' => $request->per_page ?? 10,
                    'current_page' => 1
                ]
            ]);
        }

        $events = Event::whereIn('category_id', $userInterests)
            ->whereNotNull('published_at')
            ->with(['attendees.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with(['likes.user' => function ($query) {
                return $query->select('id', 'avatar');
            }])
            ->with('tiers')
            ->with('meeting_link')
            ->withCount('attendees')
            ->withCount('likes')
            ->withCount('shares')
            ->orderBy('views', 'desc')
            ->paginate($request->per_page ?? 10);

        foreach ($events as $event) {
            $event->engagement_score = $this->calculateEngagementScore($event);

            $attendeesAvatars = $event->attendees->pluck('user.avatar')->filter()->toArray();
            $likesAvatars = $event->likes->pluck('user.avatar')->filter()->toArray();

            $event->attendees_avatars = json_encode($attendeesAvatars);
            $event->likes_avatars = json_encode($likesAvatars);
            $event->is_liked = $event->likes_count > 0;

            unset($event->likes);
            unset($event->attendees);
        }

        return response()->json([
            'events' => $events,
            'message' => 'Recommended events retrieved successfully'
        ]);
    }

    public function search(Request $request): JsonResponse
    {
        $query = Event::query();

        if ($request->has('title') && !empty($request->input('title'))) {
            $searchTerm = $request->input('title');

            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', $searchTerm)
                    ->orWhere('title', 'like', $searchTerm . '%')
                    ->orWhere('title', 'like', '%' . $searchTerm . '%')
                    ->orWhereRaw('MATCH(title) AGAINST (? IN BOOLEAN MODE)', [$searchTerm . '*'])
                    ->orWhereRaw('levenshtein(title, ?) <= 2', [$searchTerm]);
            });
        }

        if ($request->has('categories') && !empty($request->input('categories'))) {
            $categoryIds = is_array($request->input('categories'))
                ? $request->input('categories')
                : json_decode($request->input('categories'));

            if (count($categoryIds) > 0) {
                $query->whereHas('category', function ($q) use ($categoryIds) {
                    $q->whereIn('id', $categoryIds);
                });
            }
        }

        if ($request->has('dateRange') && $request->input('dateRange')) {
            $dates = explode(',', $request->input('dateRange'));
            if (count($dates) == 2) {
                $query->whereDate('created_at', '>=', $dates[0])
                    ->whereDate('created_at', '<=', $dates[1]);
            }
        }

        if ($request->has('location') && $request->input('location')) {
            $query->where(function ($q) use ($request) {
                $location = $request->input('location');
                $q->where('location', 'like', '%' . $location . '%')
                    ->orWhere('address', 'like', '%' . $location . '%')
                    ->orWhere('city', 'like', '%' . $location . '%')
                    ->orWhere('country', 'like', '%' . $location . '%');
            });
        }

        if ($request->has('priceRange') && $request->input('priceRange')) {
            $prices = explode(',', $request->input('priceRange'));
            if (count($prices) == 2) {
                $query->whereBetween('price', [$prices[0], $prices[1]]);
            }
        }

        if ($request->has('title')) {
            $searchTerm = $request->input('title');
            $query->orderByRaw('CASE
            WHEN title = ? THEN 1
            WHEN title LIKE ? THEN 2
            WHEN title LIKE ? THEN 3
            ELSE 4
        END', [
                $searchTerm,
                $searchTerm . '%',
                '%' . $searchTerm . '%'
            ]);
        }

        $perPage = $request->input('per_page', 10);
        $events = $query->paginate($perPage);

        return response()->json(['events' => $events], 200);
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'type' => 'required|integer',
            'visibility' => 'required|integer',
            'category' => 'required|integer',
            'cover_art' => 'required|image|mimes:jpeg,png,webp,jpg,gif,svg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $coverImage = 'cover_art.png';

        if ($request->file('cover_art')) {
            $image = $request->file('cover_art');
            $coverImage = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/events'), $coverImage);
        }

        $lat = "";
        $long = "";

        if ($request->latitude == null || $request->longitude == null) {
            if ($request->location !== "Remote") {
                $address = explode(",", $request->location,);
                $geocoder = new GeocodingService();
                $latlng = $geocoder->getCoordinatesFromAddress($address[0], $address[1], $address[2]);
                $lat = $latlng["latitude"];
                $long = $latlng["longitude"];
            }
        }

        $event = Event::create([
            'user_id' => $request->user()->id,
            'title' => $request->title,
            'slug' =>  Str::slug($request->title, "-"),
            'type_id' => $request->type,
            'category_id' => $request->category,
            'description' => $request->description,
            'location' => $request->location,
            'latitude' => $request->latitude ?? $lat,
            'longitude' => $request->longitude ?? $long,
            'start' => $request->start_date,
            'end' => $request->end_date,
            'visibility_id' => $request->visibility,
            'cover_art' => $coverImage
        ]);

        if ($request->meeting_link) {
            MeetingLink::create([
                'event_id' => $event->id,
                'link' => $request->meeting_link
            ]);
        }

        $pushNotificationsController = new PushNotificationController();
        $pushNotificationsController->createTopic(Str::slug($event->title));

        if ($request->has('social_links')) {
            $this->storeSocialLinks($event, $request->social_links);
        }

        return response()->json(['message' => 'Event created successfully', 'event' => $event], 201);
    }

    public function read(Request $request, $id)
    {

        $user = $request->user();

        $eventQuery = Event::with([
            'user' => function ($query) {
                $query->withCount(['followers', 'following']);
            },
            'tiers',
            'type',
            'visibility',
            'category',
            'ratings',
            'statuses',
            'sponsors.sponsor',
            'meeting_link'
        ]);

        if ($user) {
            $eventQuery->with([
                'notification' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                },
                'attendees' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                },
                'likes' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                }
            ]);
        }

        $event = $eventQuery->findOrFail($id);

        if (!$this->canViewEvent($event, $user)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        if ($user) {
            $event->notifications_enabled = $event->notification->isNotEmpty();
            $event->is_attendee = $event->attendees->isNotEmpty();
            $event->is_liked = $event->likes->isNotEmpty();
            unset($event->notification, $event->attendees, $event->likes);
        } else {
            $event->notifications_enabled = false;
            $event->is_attendee = false;
            $event->is_liked = false;
        }

        $this->incrementView($event->id, $request);

        return response()->json([
            'event' => $event,
        ], 200);
    }

    public function readBySlug(Request $request, $slug): JsonResponse
    {
        $user = $request->user();

        $eventQuery = Event::with([
            'user' => function ($query) {
                $query->withCount(['followers', 'following'])
                    ->withAvg('ratings', 'rating');
            },
            'attendees.user' => function ($query) {
                return $query->select('id', 'avatar');
            },
            'likes.user' => function ($query) {
                return $query->select('id', 'avatar');
            },
            'tiers',
            'type',
            'visibility',
            'category',
            'ratings',
            'statuses',
            'meeting_link',
            'sponsors.sponsor'
        ])
            ->withCount('attendees')
            ->withCount('likes')
            ->withCount('shares')
            ->withAvg('ratings', 'organization')
            ->withAvg('ratings', 'content')
            ->withAvg('ratings', 'technical')
            ->withAvg('ratings', 'engagement')
            ->withAvg('ratings', 'rating');

        if ($user) {
            $eventQuery->with([
                'notification' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                },
                'attendees' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                },
                'likes' => function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                }
            ]);
        }

        $event = $eventQuery->where('slug', $slug)->firstOrFail();

        if (!$this->canViewEvent($event, $user)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Process avatars for attendees and likes
        $attendeesAvatars = $event->attendees->pluck('user.avatar')->filter()->toArray();
        $likesAvatars = $event->likes->pluck('user.avatar')->filter()->toArray();

        $event->attendees_avatars = json_encode($attendeesAvatars);
        $event->likes_avatars = json_encode($likesAvatars);

        if ($user) {
            $event->notifications_enabled = $event->notification->isNotEmpty();
            $event->is_attendee = $event->attendees->contains('user_id', $user->id);
            $event->is_liked = $event->likes->contains('user_id', $user->id);
            unset($event->notification);
        } else {
            $event->notifications_enabled = false;
            $event->is_attendee = false;
            $event->is_liked = false;
        }

        // Clean up the collections to avoid exposing user data
        unset($event->attendees, $event->likes);

        if ($event->relationLoaded('ratings')) {
            $event->highlights = $event->ratings->map(function ($rating) {
                $category = 'neutral';
                if ($rating->rating >= 4) {
                    $category = 'positive';
                } elseif ($rating->rating <= 2) {
                    $category = 'negative';
                }

                return [
                    'id' => $rating->id,
                    'text' => strip_tags($rating->review),
                    'timestamp' => $rating->created_at,
                    'category' => $category,
                ];
            });
        }

        $relatedEvents = Event::with(['user', 'type', 'category'])
            ->where('category_id', $event->category_id)
            ->where('id', '!=', $event->id)
            ->when(!$user, function ($query) {
                $query->whereHas('visibility', function ($q) {
                    $q->where('name', 'public');
                });
            })
            ->latest()
            ->take(4)
            ->get();

        $this->incrementView($event->id, $request);

        return response()->json([
            'event' => $event,
            'related_events' => $relatedEvents
        ], 200);
    }

    private function canViewEvent(Event $event, ?User $user): bool
    {
        $visibility = $event->visibility->name ?? 'private';
        switch ($visibility) {
            case 'public':
                return true;
            case 'private':
                return $user && ($user->id === $event->user_id || $this->hasSpecialAccess($user, $event));
            case 'followers_only':
                $eventCreator = $event->relationLoaded('user') ? $event->user : User::find($event->user_id);
                return $user && ($user->id === $event->user_id || $this->isFollower($user, $eventCreator));
            default:
                return false;
        }
    }


    private function hasSpecialAccess(User $user, Event $event): bool
    {
        return $user->hasRole(['admin', 'moderator']) || $event->attendees->contains('user_id', $user->id);
    }


    private function isFollower(User $user, User $eventCreator): bool
    {
        return $eventCreator->followers()->where('follower_id', $user->id)->exists();
    }

    public function update(Request $request, $eventId)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'location' => 'sometimes|string|max:255',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date',
            'type' => 'sometimes|integer',
            'visibility' => 'sometimes|integer',
            'category' => 'sometimes|integer',
            'cover_art' => 'sometimes|image|mimes:jpeg,png,webp,jpg,gif,svg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $event = Event::findOrFail($eventId);

        $coverImage = $event->cover_art;
        if ($request->hasFile('cover_art')) {
            $image = $request->file('cover_art');
            $coverImage = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/events'), $coverImage);

            if ($event->cover_art && $event->cover_art !== 'cover_art.png') {
                $oldImagePath = public_path('storage/events/' . $event->cover_art);
                if (File::exists($oldImagePath)) {
                    File::delete($oldImagePath);
                }
            }
        }

        $lat = $event->latitude;
        $long = $event->longitude;

        if (($request->has('location') && $request->location !== $event->location) ||
            ($request->latitude === null || $request->longitude === null)
        ) {
            if ($request->location !== "Remote") {
                $address = explode(",", $request->location);
                $geocoder = new GeocodingService();
                $latlng = $geocoder->getCoordinatesFromAddress($address[0] ?? "", $address[1] ?? "", $address[2] ?? "");
                $lat = $latlng["latitude"];
                $long = $latlng["longitude"];
            }
        }

        $event->title = $request->title;
        $event->slug = $request->slug ?? Str::slug($request->title, "-");
        $event->type_id = $request->type;
        $event->category_id = $request->category;
        $event->description = $request->description;
        $event->location = $request->location;
        $event->latitude = $request->latitude ?? $lat;
        $event->longitude = $request->longitude ?? $long;
        $event->start = $request->start_date;
        $event->end = $request->end_date;
        $event->visibility_id = $request->visibility;
        $event->cover_art = $coverImage;

        $event->save();

        if ($request->has('meeting_link')) {
            MeetingLink::updateOrCreate(
                ['event_id' => $event->id],
                ['link' => $request->meeting_link]
            );
        }

        if ($request->has('social_links')) {
            $event->socialLinks()->delete();
            $this->storeSocialLinks($event, $request->social_links);
        }

        return response()->json([
            'message' => 'Event updated successfully',
            'event' => $event->fresh()
        ], 200);
    }

    public function delete(Request $request, $id)
    {
        $event = Event::find($id);
        if (!$event) {
            return response()->json(['error' => 'Event not found'], 401);
        }
        $event->delete();
        return response()->json(['message' => 'Event deleted successfully'], 200);
    }

    public function addAttendee(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $event = Event::find($request->event_id);

        if (!$event) {
            return response()->json(['error' => 'Event not found'], 400);
        }

        $attendee = EventAttendee::where('event_id', $request->event_id)
            ->where('user_id', $request->user()->id)
            ->first();

        if ($attendee) {
            $attendee->delete();
            return response()->json(['error' => 'You have removed your attendance status successfully'], 400);
        }

        $attendee = EventAttendee::create([
            'event_id' => $request->event_id,
            'user_id' => $request->user()->id
        ]);

        return response()->json(['message' => 'You have been added to ' . $event->title . ' event successfully', 'attendee' => $attendee], 201);
    }

    private function storeSocialLinks($event, $socialLinks)
    {
        foreach ($socialLinks as $link) {
            EventSocialLink::create([
                'event_id' => $event->id,
                'platform' => $link['platform'],
                'link' => $link['link'],
            ]);
        }
    }

    private function updateSocialLinks($event, $socialLinks)
    {
        EventSocialLink::where('event_id', $event->id)->delete();
        $this->storeSocialLinks($event, $socialLinks);
    }

    /** @method getNearbyEvents Get nearby events based on latitude, longitude, distance, and city.
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNearbyEvents(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'nullable|numeric',
            'city' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }


        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        $distance = $request->input('radius', 50);
        $city = $request->input('city');

        $events = Event::whereRaw(
            '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
            [$latitude, $longitude, $latitude, $distance]
        )
            ->when($city, function ($query, $city) {
                return $query->where('location', 'LIKE', '%' . $city . '%');
            })
            ->paginate($request->per_page ?? 10);


        return response()->json(['events' => $events], 200);
    }

    public function likeEvent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }
        $userId = $request->user()->id;

        $event = Event::find($request->event_id);
        if (!$event) {
            return response()->json(['error' => 'Event not found'], 401);
        }

        $existingLike = EventLike::where('event_id', $request->event_id)
            ->where('user_id', $userId)
            ->first();

        if ($existingLike) {
            $existingLike->delete();
            return response()->json(['message' => 'You have unliked this event'], 200);
        }

        EventLike::create([
            'user_id' => $userId,
            'event_id' => $event->id,
        ]);

        return response()->json(['message' => 'Event liked successfully'], 201);
    }

    public function userEvents(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $perPage = $request->input('per_page', 10);

            // Get date range parameters if provided (for calendar view)
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $query = Event::with(['category', 'user'])
                ->where('user_id', $user->id);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $start = Carbon::parse($startDate)->startOfDay();
                $end = Carbon::parse($endDate)->endOfDay();
                $query->where('start', '>=', $start)
                      ->where('start', '<=', $end);
            }

            $events = $query->orderBy('start', 'asc')->paginate($perPage);

            // Format events for frontend compatibility
            $formattedEvents = $events->getCollection()->map(function ($event) {
                return $this->formatEventForCalendar($event);
            });

            $events->setCollection($formattedEvents);

            return response()->json([
                'success' => true,
                'events' => $events
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch user events',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function share(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }
        $userId = $request->user()->id;
        $event = Event::find($request->event_id);
        if (!$event) {
            return response()->json(['error' => 'Event not found'], 401);
        }
        EventShare::create([
            'user_id' => $userId,
            'event_id' => $event->id,
        ]);
        return response()->json(['message' => 'Event shared successfully'], 201);
    }

    public function calendar(Request $request): JsonResponse
    {
        try {
            // Get date range parameters - try both parameter names for compatibility
            $startDate = $request->input('start_date') ?? $request->input('start');
            $endDate = $request->input('end_date') ?? $request->input('end');

            // If no date range provided, default to current month
            if (!$startDate || !$endDate) {
                $startDate = Carbon::now()->startOfMonth()->toDateString();
                $endDate = Carbon::now()->endOfMonth()->toDateString();
            }

            $start = Carbon::parse($startDate)->startOfDay();
            $end = Carbon::parse($endDate)->endOfDay();

            // Get events within the date range with all necessary fields
            $events = Event::with(['category', 'user'])
                ->where('start', '>=', $start)
                ->where('start', '<=', $end)
                ->orderBy('start', 'asc')
                ->get()
                ->map(function ($event) {
                    return $this->formatEventForCalendar($event);
                });

            return response()->json([
                'success' => true,
                'events' => $events,
                'total' => $events->count(),
                'date_range' => [
                    'start' => $startDate,
                    'end' => $endDate
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch calendar events',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format event for calendar/frontend compatibility
     */
    private function formatEventForCalendar($event): array
    {
        try {
            $startDateTime = $event->start ? Carbon::parse($event->start) : Carbon::now();
            $endDateTime = $event->end ? Carbon::parse($event->end) : null;

            return [
                'id' => $event->id,
                'title' => $event->title ?? 'Untitled Event',
                'description' => $event->description ?? '',
                'start_date' => $startDateTime->toDateString(),
                'end_date' => $endDateTime ? $endDateTime->toDateString() : $startDateTime->toDateString(),
                'start_time' => $startDateTime->format('H:i'),
                'end_time' => $endDateTime ? $endDateTime->format('H:i') : null,
                'location' => $event->location ?? '',
                'status' => 'published', // Default status, adjust as needed
                'category' => $event->category ? [
                    'id' => $event->category->id,
                    'name' => $event->category->name
                ] : null,
                'user' => $event->user ? [
                    'id' => $event->user->id,
                    'name' => $event->user->name
                ] : null,
            ];
        } catch (\Exception $e) {
            // Fallback for malformed dates
            return [
                'id' => $event->id,
                'title' => $event->title ?? 'Untitled Event',
                'description' => $event->description ?? '',
                'start_date' => Carbon::now()->toDateString(),
                'end_date' => Carbon::now()->toDateString(),
                'start_time' => '00:00',
                'end_time' => null,
                'location' => $event->location ?? '',
                'status' => 'published',
                'category' => $event->category ? [
                    'id' => $event->category->id,
                    'name' => $event->category->name
                ] : null,
                'user' => $event->user ? [
                    'id' => $event->user->id,
                    'name' => $event->user->name
                ] : null,
            ];
        }
    }

    public function allowNotifications(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $userId = $request->user()->id;
        $event = Event::find($request->event_id);
        if (!$event) {
            return response()->json(['error' => 'Event not found'], 401);
        }

        $existingNotification = EventNotification::where('user_id', $userId)
            ->where('event_id', $event->id)
            ->first();

        if ($existingNotification) {
            $existingNotification->delete();
            return response()->json(['message' => 'Notifications for this event disabled successfully'], 201);
        } else {
            EventNotification::create([
                'user_id' => $userId,
                'event_id' => $event->id,
                'is_enabled' => 1
            ]);
        }

        return response()->json(['message' => 'Notifications for this event enabled successfully'], 201);
    }

    public function publish(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'publish_type' => 'required|string|in:immediate,schedule',
            'scheduled_at' => 'required_if:publish_type,schedule|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $event = Event::find($request->event_id);
        if (!$event) {
            return response()->json(['error' => 'Event not found'], 401);
        }

        if ($request->publish_type === 'immediate') {
            $event->published_at = Carbon::now();
            $event->save();
            return response()->json(['message' => 'Event published immediately'], 200);
        } elseif ($request->publish_type === 'schedule') {
            $event->published_at = Carbon::parse($request->scheduled_at);
            $event->save();
            return response()->json(['message' => 'Event scheduled for publication'], 200);
        }

        return response()->json(['error' => 'Invalid publish type'], 400);
    }


    public function addEventRetailers(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer',
            'retailers' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        foreach ($request->retailers as $retailer) {
            $eventRetailer = new EventRetailer();
            $eventRetailer->event_id = $request->event_id;
            $eventRetailer->retailer_id = $retailer;
            $eventRetailer->save();
        }

        return response()->json(['message' => 'Retailers added successfully'], 200);
    }

    public function addEventSponsors(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|integer|exists:events,id',
            'sponsors' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $addedSponsors = 0;
        $duplicates = 0;

        foreach (json_decode($request->sponsors[0]) as $sponsor) {
            try {
                EventSponsor::firstOrCreate(
                    [
                        'event_id' => $request->event_id,
                        'sponsor_id' => $sponsor
                    ]
                );
                $addedSponsors++;
            } catch (\Illuminate\Database\QueryException $e) {
                $duplicates++;
                continue;
            }
        }

        return response()->json([
            'message' => 'Sponsors processed successfully'
        ], 200);
    }

    public function incrementView($id, Request $request)
    {
        $event = Event::findOrFail($id);

        $userId = $request->user()->id ?? request()->ip();
        $viewed = cache()->get('viewed_event_' . $userId . '_' . $event->id);

        if (!$viewed) {
            $event->increment('views');
            cache()->put('viewed_event_' . $userId . '_' . $event->id, true, now()->addMinutes(10));
        }
    }
}
